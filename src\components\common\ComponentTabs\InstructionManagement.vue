<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onMounted, shallowRef, watch } from 'vue';
import { ElMessage, TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition } from '@/types';
import { formatDateTime } from '@/script/formatter';

// 定义指令接口
interface InstructionInfo {
  id: string;
  instructionType: string;
  status: number; // 1: 待审核, 2: 已通过, 3: 已驳回
  createTime: string;
  createUser: string;
  auditTime?: string;
  auditUser?: string;
  remark?: string;
}

// 定义指令订单接口
interface InstructionOrderInfo {
  id: string;
  instructionId: string;
  instrument: string;
  instrumentName: string;
  direction: number;
  volume: number;
  price: number;
  orderType: string;
  status: number;
  createTime: string;
}

const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : any;
}>();

// 指令列表
const instructions = shallowRef<InstructionInfo[]>([]);

// 选中的指令
const selectedInstruction = shallowRef<InstructionInfo>();

// 指令对应的订单列表
const instructionOrders = shallowRef<InstructionOrderInfo[]>([]);

// 指令列表列定义
const instructionColumns = [
  {
    key: 'id',
    title: 'ID',
    width: 100,
    sortable: true,
  },
  {
    key: 'instructionType',
    title: '指令类型',
    width: 120,
    sortable: true,
  },
  {
    key: 'status',
    title: '状态',
    width: 100,
    sortable: true,
    cellRenderer: (params: any) => {
      const statusMap = {
        1: { label: '待审核', color: 'text-orange-500' },
        2: { label: '已通过', color: 'text-green-500' },
        3: { label: '已驳回', color: 'text-red-500' },
      };
      const status = statusMap[params.cellData as keyof typeof statusMap];
      return <span class={status?.color}>{status?.label || '未知'}</span>;
    },
  },
  {
    key: 'createUser',
    title: '创建人',
    width: 120,
    sortable: true,
  },
  {
    key: 'createTime',
    title: '创建时间',
    width: 160,
    sortable: true,
    cellRenderer: (params: any) => formatDateTime(params.cellData, 'MM-dd hh:mm:ss'),
  },
  {
    key: 'auditUser',
    title: '审核人',
    width: 120,
    sortable: true,
  },
  {
    key: 'auditTime',
    title: '审核时间',
    width: 160,
    sortable: true,
    cellRenderer: (params: any) =>
      params.cellData ? formatDateTime(params.cellData, 'MM-dd hh:mm:ss') : '--',
  },
  {
    key: 'remark',
    title: '备注',
    width: 200,
    sortable: true,
  },
] as ColumnDefinition<any>;

// 订单列表列定义
const orderColumns = [
  {
    key: 'id',
    title: '订单ID',
    width: 120,
    sortable: true,
  },
  {
    key: 'instrument',
    title: '合约代码',
    width: 120,
    sortable: true,
  },
  {
    key: 'instrumentName',
    title: '合约名称',
    width: 150,
    sortable: true,
  },
  {
    key: 'direction',
    title: '方向',
    width: 80,
    sortable: true,
    cellRenderer: (params: any) => {
      return (
        <span class={params.cellData === 1 ? 'text-red-500' : 'text-green-500'}>
          {params.cellData === 1 ? '买入' : '卖出'}
        </span>
      );
    },
  },
  {
    key: 'volume',
    title: '数量',
    width: 100,
    align: 'right',
    sortable: true,
  },
  {
    key: 'price',
    title: '价格',
    width: 100,
    align: 'right',
    sortable: true,
    cellRenderer: (params: any) => params.cellData.toFixed(2),
  },
  {
    key: 'orderType',
    title: '订单类型',
    width: 100,
    sortable: true,
  },
  {
    key: 'status',
    title: '状态',
    width: 100,
    sortable: true,
    cellRenderer: (params: any) => {
      const statusMap = {
        1: { label: '待执行', color: 'text-orange-500' },
        2: { label: '已执行', color: 'text-green-500' },
        3: { label: '已取消', color: 'text-gray-500' },
      };
      const status = statusMap[params.cellData as keyof typeof statusMap];
      return <span class={status?.color}>{status?.label || '未知'}</span>;
    },
  },
  {
    key: 'createTime',
    title: '创建时间',
    width: 160,
    sortable: true,
    cellRenderer: (params: any) => formatDateTime(params.cellData, 'MM-dd hh:mm:ss'),
  },
] as ColumnDefinition<any>;

// 加载指令列表
const loadInstructions = async () => {
  if (!activeItem) return;

  try {
    // 模拟数据
    const mockInstructions: InstructionInfo[] = [
      {
        id: 'INS001',
        instructionType: '普通下单',
        status: 1,
        createTime: new Date().toISOString(),
        createUser: '张三',
        remark: '测试指令1',
      },
      {
        id: 'INS002',
        instructionType: '篮子下单',
        status: 2,
        createTime: new Date(Date.now() - 3600000).toISOString(),
        createUser: '李四',
        auditTime: new Date(Date.now() - 1800000).toISOString(),
        auditUser: '王五',
        remark: '测试指令2',
      },
      {
        id: 'INS003',
        instructionType: '算法下单',
        status: 3,
        createTime: new Date(Date.now() - 7200000).toISOString(),
        createUser: '赵六',
        auditTime: new Date(Date.now() - 3600000).toISOString(),
        auditUser: '钱七',
        remark: '测试指令3',
      },
    ];

    instructions.value = mockInstructions;
  } catch (error) {
    console.error('加载指令列表失败:', error);
    ElMessage.error('加载指令列表失败');
  }
};

// 加载指令对应的订单列表
const loadInstructionOrders = async (instructionId: string) => {
  try {
    // 模拟数据
    const mockOrders: InstructionOrderInfo[] = [
      {
        id: 'ORD001',
        instructionId,
        instrument: '000001.SZ',
        instrumentName: '平安银行',
        direction: 1,
        volume: 1000,
        price: 12.34,
        orderType: '限价单',
        status: 1,
        createTime: new Date().toISOString(),
      },
      {
        id: 'ORD002',
        instructionId,
        instrument: '000002.SZ',
        instrumentName: '万科A',
        direction: -1,
        volume: 500,
        price: 23.45,
        orderType: '市价单',
        status: 2,
        createTime: new Date(Date.now() - 1800000).toISOString(),
      },
    ];

    instructionOrders.value = mockOrders;
  } catch (error) {
    console.error('加载订单列表失败:', error);
    ElMessage.error('加载订单列表失败');
  }
};

// 处理指令行点击
const handleInstructionRowClick = (instruction: InstructionInfo) => {
  selectedInstruction.value = instruction;
  loadInstructionOrders(instruction.id);
};

// 监听activeItem变化
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      loadInstructions();
    }
  },
  { deep: true },
);

onMounted(() => {
  if (activeItem) {
    loadInstructions();
  }
});
</script>

<template>
  <div class="instruction-management" h-full flex="~ col">
    <!-- 指令列表 -->
    <div class="instruction-list" h-300>
      <VirtualizedTable
        :columns="instructionColumns"
        :data="instructions"
        :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
        :show-toolbar="false"
        :row-height="36"
        :header-row-height="36"
        @row-click="handleInstructionRowClick"
      >
        <template #actions>
          <div class="actions" flex aic>
            <el-button @click="loadInstructions" size="small" color="var(--g-primary)">
              刷新
            </el-button>
          </div>
        </template>
      </VirtualizedTable>
    </div>

    <!-- 订单详情 -->
    <div class="order-details" flex-1 min-h-1>
      <div v-if="selectedInstruction" class="order-list" h-full>
        <div class="order-header" h-40 flex aic px-16>
          <span class="title" fs-14 fw-600>指令订单详情 - {{ selectedInstruction.id }}</span>
        </div>

        <div class="order-content" flex-1 min-h-1>
          <VirtualizedTable
            :columns="orderColumns"
            :data="instructionOrders"
            :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
            :show-toolbar="false"
            :row-height="36"
            :header-row-height="36"
          />
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state" flex="~ col" aic jcc h-full c-gray-500>
        <div fs-14>请点击上方指令查看订单详情</div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>

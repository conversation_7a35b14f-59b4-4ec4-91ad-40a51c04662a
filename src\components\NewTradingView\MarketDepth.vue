<script setup lang="ts">
import { computed, onBeforeUnmount, onMounted, shallowRef, watch } from 'vue';
import type { InstrumentInfo, StandardTick } from '@/types';
import { formatNumber, getColorClass } from '@/script/formatter';
import { TickService } from '@/api';
import { TickType } from '../../../../xtrade-sdk';

const { selectedInstrument } = defineProps<{
  selectedInstrument?: InstrumentInfo;
}>();

// 最新tick数据
const lastTick = shallowRef<StandardTick>();

// 价格档位数据
const priceLevels = computed(() => {
  if (!lastTick.value) return [];

  return [
    {
      level: 5,
      type: 'sell',
      price: lastTick.value.askPrice[4],
      volume: lastTick.value.askVolume[4],
    },
    {
      level: 4,
      type: 'sell',
      price: lastTick.value.askPrice[3],
      volume: lastTick.value.askVolume[3],
    },
    {
      level: 3,
      type: 'sell',
      price: lastTick.value.askPrice[2],
      volume: lastTick.value.askVolume[2],
    },
    {
      level: 2,
      type: 'sell',
      price: lastTick.value.askPrice[1],
      volume: lastTick.value.askVolume[1],
    },
    {
      level: 1,
      type: 'sell',
      price: lastTick.value.askPrice[0],
      volume: lastTick.value.askVolume[0],
    },
    {
      level: 1,
      type: 'buy',
      price: lastTick.value.bidPrice[0],
      volume: lastTick.value.bidVolume[0],
    },
    {
      level: 2,
      type: 'buy',
      price: lastTick.value.bidPrice[1],
      volume: lastTick.value.bidVolume[1],
    },
    {
      level: 3,
      type: 'buy',
      price: lastTick.value.bidPrice[2],
      volume: lastTick.value.bidVolume[2],
    },
    {
      level: 4,
      type: 'buy',
      price: lastTick.value.bidPrice[3],
      volume: lastTick.value.bidVolume[3],
    },
    {
      level: 5,
      type: 'buy',
      price: lastTick.value.bidPrice[4],
      volume: lastTick.value.bidVolume[4],
    },
  ];
});

// 基本信息
const basicInfo = computed(() => {
  if (!lastTick.value) return [];

  const changeAmount = lastTick.value.lastPrice - lastTick.value.preClosePrice;
  const changePercent = (changeAmount / lastTick.value.preClosePrice) * 100;

  return [
    {
      label: '最新价',
      value: formatNumber(lastTick.value.lastPrice, { default: '--' }),
      change: formatNumber(changeAmount, { default: '--' }),
      percent: formatNumber(changePercent, { percent: true, default: '--' }),
      needColor: true,
      colorValue: changeAmount,
    },
    {
      label: '今开',
      value: formatNumber(lastTick.value.openPrice, { default: '--' }),
      needColor: true,
      colorValue: lastTick.value.openPrice - lastTick.value.preClosePrice,
    },
    {
      label: '昨收',
      value: formatNumber(lastTick.value.preClosePrice, { default: '--' }),
      needColor: false,
    },
    {
      label: '最高',
      value: formatNumber(lastTick.value.highPrice, { default: '--' }),
      needColor: true,
      colorValue: lastTick.value.highPrice - lastTick.value.preClosePrice,
    },
    {
      label: '最低',
      value: formatNumber(lastTick.value.lowPrice, { default: '--' }),
      needColor: true,
      colorValue: lastTick.value.lowPrice - lastTick.value.preClosePrice,
    },
    {
      label: '成交量',
      value: formatNumber(lastTick.value.volume, { default: '--' }),
      needColor: false,
    },
    {
      label: '成交额',
      value: formatNumber(lastTick.value.turnover, { default: '--' }),
      needColor: false,
    },
  ];
});

// 更新tick数据
const updateTick = (data: StandardTick) => {
  lastTick.value = data;
};

// 监听合约变化，订阅/取消订阅tick数据
watch(
  () => selectedInstrument,
  async (newInstrument, oldInstrument) => {
    // 取消订阅旧合约
    if (oldInstrument) {
      await TickService.unsubscribeTick(oldInstrument.instrument, TickType.tick, updateTick);
      lastTick.value = undefined;
    }

    // 订阅新合约
    if (newInstrument) {
      TickService.subscribeTick(newInstrument.instrument, TickType.tick, updateTick);
    }
  },
  { immediate: true },
);

onBeforeUnmount(async () => {
  if (selectedInstrument) {
    await TickService.unsubscribeTick(selectedInstrument.instrument, TickType.tick, updateTick);
  }
});
</script>

<template>
  <div class="market-depth" h-full flex="~ col">
    <!-- 合约信息 -->
    <div class="instrument-info" p-16 v-if="selectedInstrument">
      <div class="instrument-name" fs-16 fw-600 mb-8>
        {{ selectedInstrument.instrumentName }}
      </div>
      <div class="instrument-code" fs-14 c-gray-500>
        {{ selectedInstrument.instrument }}
      </div>
    </div>

    <!-- 基本信息 -->
    <div class="basic-info" p-16 v-if="lastTick">
      <div v-for="info in basicInfo" :key="info.label" class="info-item" flex jcsb aic mb-8>
        <span class="label" fs-12 c-gray-500>{{ info.label }}</span>
        <div class="value-group" flex aic gap-8>
          <span
            class="value"
            fs-14
            fw-500
            :class="info.needColor ? getColorClass(info.colorValue) : ''"
          >
            {{ info.value }}
          </span>
          <div v-if="info.change && info.percent" class="change-info" flex="~ col" aic>
            <span class="change" fs-12 :class="getColorClass(info.colorValue)">
              {{ info.change }}
            </span>
            <span class="percent" fs-12 :class="getColorClass(info.colorValue)">
              {{ info.percent }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 买卖档位 -->
    <div class="price-levels" flex-1 p-16 of-auto>
      <div class="levels-header" flex jcsb aic mb-12 fs-12 c-gray-500>
        <span>档位</span>
        <span>价格</span>
        <span>数量</span>
      </div>

      <div
        v-for="level in priceLevels"
        :key="`${level.type}-${level.level}`"
        class="level-item"
        flex
        jcsb
        aic
        h-32
        px-8
        rounded-4
        cursor-pointer
        transition="all duration-100"
        hover="bg-[--g-bg-hover-2]"
      >
        <!-- 档位标签 -->
        <span
          class="level-label"
          fs-12
          :class="level.type === 'sell' ? 'c-red-500' : 'c-green-500'"
        >
          {{ level.type === 'sell' ? '卖' + level.level : '买' + level.level }}
        </span>

        <!-- 价格 -->
        <span
          class="price"
          fs-14
          fw-500
          :class="lastTick ? getColorClass(level.price! - lastTick.preClosePrice) : ''"
        >
          {{ formatNumber(level.price, { default: '--' }) }}
        </span>

        <!-- 数量 -->
        <span class="volume" fs-12 c-gray-600>
          {{ formatNumber(level.volume, { default: '--' }) }}
        </span>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!selectedInstrument" class="empty-state" flex-1 flex="~ col" aic jcc c-gray-500>
      <div fs-14>请选择合约查看盘口信息</div>
    </div>
  </div>
</template>

<style scoped>
.market-depth {
}

.header {
  background-color: var(--g-bg);
}

.title {
  color: var(--g-text-color-1);
}

.instrument-name {
  color: var(--g-text-color-1);
}

.info-item:last-child {
  margin-bottom: 0;
}

.level-item:hover {
  background-color: var(--g-bg-hover-2);
}

.change-info {
  min-width: 60px;
  text-align: right;
}
</style>

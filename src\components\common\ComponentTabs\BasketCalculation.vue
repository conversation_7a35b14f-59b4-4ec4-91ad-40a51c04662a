<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onMounted, shallowRef, ref } from 'vue';
import { ElMessage, TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition } from '@/types';
import { formatNumber } from '@/script/formatter';

// 定义篮子试算结果接口
interface BasketCalculationResult {
  id: string;
  instrument: string;
  instrumentName: string;
  direction: number; // 1: 买入, -1: 卖出
  targetVolume: number; // 目标数量
  currentVolume: number; // 当前持仓
  tradeVolume: number; // 交易数量
  price: number; // 价格
  amount: number; // 金额
  weight: number; // 权重
  status: string; // 状态
}

const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : any;
}>();

// 篮子试算结果
const calculationResults = shallowRef<BasketCalculationResult[]>([]);

// 篮子参数
const basketParams = ref({
  basketId: '',
  basketName: '',
  tradeAmount: 1000000, // 交易金额
  priceType: 1, // 价格类型：1-最新价，2-涨停价，3-跌停价
  priceOffset: 0, // 价格偏移
});

// 篮子选项
const basketOptions = ref([
  { value: 'BASKET001', label: '沪深300篮子' },
  { value: 'BASKET002', label: '中证500篮子' },
  { value: 'BASKET003', label: '创业板篮子' },
]);

// 价格类型选项
const priceTypeOptions = [
  { value: 1, label: '最新价' },
  { value: 2, label: '涨停价' },
  { value: 3, label: '跌停价' },
];

// 表格列定义
const columns = [
  {
    key: 'instrument',
    title: '合约代码',
    width: 120,
    sortable: true,
  },
  {
    key: 'instrumentName',
    title: '合约名称',
    width: 150,
    sortable: true,
  },
  {
    key: 'direction',
    title: '方向',
    width: 80,
    sortable: true,
    cellRenderer: (params: any) => {
      return (
        <span class={params.cellData === 1 ? 'text-red-500' : 'text-green-500'}>
          {params.cellData === 1 ? '买入' : '卖出'}
        </span>
      );
    },
  },
  {
    key: 'currentVolume',
    title: '当前持仓',
    width: 100,
    align: 'right',
    sortable: true,
    cellRenderer: (params: any) => formatNumber(params.cellData, { default: '0' }),
  },
  {
    key: 'targetVolume',
    title: '目标数量',
    width: 100,
    align: 'right',
    sortable: true,
    cellRenderer: (params: any) => formatNumber(params.cellData, { default: '0' }),
  },
  {
    key: 'tradeVolume',
    title: '交易数量',
    width: 100,
    align: 'right',
    sortable: true,
    cellRenderer: (params: any) => {
      const value = formatNumber(params.cellData, { default: '0' });
      const color =
        params.cellData > 0 ? 'text-red-500' : params.cellData < 0 ? 'text-green-500' : '';
      return <span class={color}>{value}</span>;
    },
  },
  {
    key: 'price',
    title: '价格',
    width: 100,
    align: 'right',
    sortable: true,
    cellRenderer: (params: any) => formatNumber(params.cellData, { fix: 2, default: '--' }),
  },
  {
    key: 'amount',
    title: '金额',
    width: 120,
    align: 'right',
    sortable: true,
    cellRenderer: (params: any) => formatNumber(params.cellData, { default: '0' }),
  },
  {
    key: 'weight',
    title: '权重(%)',
    width: 100,
    align: 'right',
    sortable: true,
    cellRenderer: (params: any) =>
      formatNumber(params.cellData * 100, { percent: true, default: '0%' }),
  },
  {
    key: 'status',
    title: '状态',
    width: 100,
    sortable: true,
    cellRenderer: (params: any) => {
      const statusMap = {
        normal: { label: '正常', color: 'text-green-500' },
        suspended: { label: '停牌', color: 'text-orange-500' },
        limit_up: { label: '涨停', color: 'text-red-500' },
        limit_down: { label: '跌停', color: 'text-green-500' },
      };
      const status = statusMap[params.cellData as keyof typeof statusMap];
      return <span class={status?.color}>{status?.label || params.cellData}</span>;
    },
  },
] as ColumnDefinition<BasketCalculationResult>;

// 统计信息
const statistics = computed(() => {
  const totalAmount = calculationResults.value.reduce(
    (sum, item) => sum + Math.abs(item.amount),
    0,
  );
  const buyAmount = calculationResults.value
    .filter(item => item.tradeVolume > 0)
    .reduce((sum, item) => sum + item.amount, 0);
  const sellAmount = calculationResults.value
    .filter(item => item.tradeVolume < 0)
    .reduce((sum, item) => sum + Math.abs(item.amount), 0);
  const stockCount = calculationResults.value.length;
  const tradeCount = calculationResults.value.filter(item => item.tradeVolume !== 0).length;

  return {
    totalAmount,
    buyAmount,
    sellAmount,
    stockCount,
    tradeCount,
  };
});

// 执行篮子试算
const executeCalculation = async () => {
  if (!basketParams.value.basketId) {
    ElMessage.warning('请选择篮子');
    return;
  }

  if (!basketParams.value.tradeAmount || basketParams.value.tradeAmount <= 0) {
    ElMessage.warning('请输入有效的交易金额');
    return;
  }

  try {
    // 模拟篮子试算结果
    const mockResults: BasketCalculationResult[] = [
      {
        id: '1',
        instrument: '000001.SZ',
        instrumentName: '平安银行',
        direction: 1,
        currentVolume: 1000,
        targetVolume: 1500,
        tradeVolume: 500,
        price: 12.34,
        amount: 6170,
        weight: 0.05,
        status: 'normal',
      },
      {
        id: '2',
        instrument: '000002.SZ',
        instrumentName: '万科A',
        direction: -1,
        currentVolume: 2000,
        targetVolume: 1500,
        tradeVolume: -500,
        price: 23.45,
        amount: -11725,
        weight: 0.08,
        status: 'normal',
      },
      {
        id: '3',
        instrument: '000858.SZ',
        instrumentName: '五粮液',
        direction: 1,
        currentVolume: 0,
        targetVolume: 100,
        tradeVolume: 100,
        price: 156.78,
        amount: 15678,
        weight: 0.12,
        status: 'suspended',
      },
      {
        id: '4',
        instrument: '600036.SH',
        instrumentName: '招商银行',
        direction: 1,
        currentVolume: 500,
        targetVolume: 800,
        tradeVolume: 300,
        price: 45.67,
        amount: 13701,
        weight: 0.1,
        status: 'limit_up',
      },
    ];

    calculationResults.value = mockResults;
    ElMessage.success('篮子试算完成');
  } catch (error) {
    console.error('篮子试算失败:', error);
    ElMessage.error('篮子试算失败');
  }
};

// 清空结果
const clearResults = () => {
  calculationResults.value = [];
};

onMounted(() => {
  // 初始化时可以加载默认篮子
  basketParams.value.basketId = basketOptions.value[0]?.value || '';
  basketParams.value.basketName = basketOptions.value[0]?.label || '';
});
</script>

<template>
  <div class="basket-calculation" h-full flex="~ col">
    <!-- 参数设置 -->
    <div class="params-section" p-16 border="b-1 solid [--g-border-color]">
      <div class="params-grid" grid="~ cols-4 gap-16">
        <!-- 篮子选择 -->
        <div class="param-item">
          <label class="label">篮子</label>
          <el-select
            v-model="basketParams.basketId"
            placeholder="选择篮子"
            @change="
              value => {
                const option = basketOptions.find(opt => opt.value === value);
                basketParams.basketName = option?.label || '';
              }
            "
          >
            <el-option
              v-for="option in basketOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>

        <!-- 交易金额 -->
        <div class="param-item">
          <label class="label">交易金额</label>
          <el-input-number
            v-model="basketParams.tradeAmount"
            :min="0"
            :step="10000"
            placeholder="请输入金额"
            w-full
          />
        </div>

        <!-- 价格类型 -->
        <div class="param-item">
          <label class="label">价格类型</label>
          <el-select v-model="basketParams.priceType" placeholder="选择价格类型">
            <el-option
              v-for="option in priceTypeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>

        <!-- 价格偏移 -->
        <div class="param-item">
          <label class="label">价格偏移(%)</label>
          <el-input-number
            v-model="basketParams.priceOffset"
            :precision="2"
            :step="0.1"
            placeholder="偏移百分比"
            w-full
          />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="actions" mt-16 flex gap-12>
        <el-button type="primary" @click="executeCalculation">试算</el-button>
        <el-button @click="clearResults">清空</el-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div
      v-if="calculationResults.length > 0"
      class="statistics-section"
      p-16
      border="b-1 solid [--g-border-color]"
    >
      <div class="stats-grid" grid="~ cols-5 gap-16">
        <div class="stat-item">
          <span class="stat-label">总金额</span>
          <span class="stat-value">{{ formatNumber(statistics.totalAmount) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">买入金额</span>
          <span class="stat-value text-red-500">{{ formatNumber(statistics.buyAmount) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">卖出金额</span>
          <span class="stat-value text-green-500">{{ formatNumber(statistics.sellAmount) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">股票数量</span>
          <span class="stat-value">{{ statistics.stockCount }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">交易数量</span>
          <span class="stat-value">{{ statistics.tradeCount }}</span>
        </div>
      </div>
    </div>

    <!-- 试算结果 -->
    <div class="results-section" flex-1 min-h-1>
      <VirtualizedTable
        :columns="columns"
        :data="calculationResults"
        :sort="{ key: 'amount', order: TableV2SortOrder.DESC }"
        :show-toolbar="false"
        :row-height="36"
        :header-row-height="36"
      />

      <!-- 空状态 -->
      <div
        v-if="calculationResults.length === 0"
        class="empty-state"
        flex="~ col"
        aic
        jcc
        h-full
        c-gray-500
      >
        <div fs-14>请设置参数并点击试算查看结果</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.basket-calculation {
}

.param-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  font-size: 14px;
  font-weight: 500;
  color: var(--g-text-color-1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--g-text-color-2);
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--g-text-color-1);
}
</style>

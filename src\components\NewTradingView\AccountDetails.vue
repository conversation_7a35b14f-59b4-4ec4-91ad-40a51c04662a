<script setup lang="ts">
import { computed, ref } from 'vue';
import ComponentTabs from '@/components/common/ComponentTabs.vue';
import type { AccountInfo, ComponentTab } from '@/types';

const { selectedAccount, tradeType } = defineProps<{
  selectedAccount?: AccountInfo;
  tradeType: 'normal' | 'algo' | 'basket';
}>();

// 当前激活的tab
const activeTab = ref('instructions');

// 定义tabs配置
const tabs = computed<ComponentTab[]>(() => {
  const baseTabs: ComponentTab[] = [
    {
      label: '指令',
      component: 'InstructionManagement',
      props: {
        activeItem: selectedAccount,
        type: 'account',
      },
    },
    {
      label: '委托',
      component: 'TodayOrders',
      props: {
        activeItem: selectedAccount,
        type: 'account',
      },
    },
    {
      label: '成交',
      component: 'TodayRecords',
      props: {
        activeItem: selectedAccount,
        type: 'account',
      },
    },
    {
      label: '持仓',
      component: 'TodayPositions',
      props: {
        activeItem: selectedAccount,
        type: 'account',
      },
    },
  ];

  // 如果是篮子交易，在前面添加篮子试算tab
  if (tradeType === 'basket') {
    baseTabs.unshift({
      label: '篮子试算',
      component: 'BasketCalculation',
      props: {
        activeItem: selectedAccount,
        type: 'account',
      },
    });
  }

  return baseTabs;
});
</script>

<template>
  <div class="account-details" h-full flex="~ col">
    <!-- 内容区域 -->
    <div class="content" flex-1 min-h-1>
      <ComponentTabs v-if="selectedAccount" :tabs="tabs" />

      <!-- 空状态 -->
      <div v-else class="empty-state" flex="~ col" aic jcc h-full c-gray-500>
        <div fs-14>请选择账号查看详情</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.account-details {
}

.header {
  background-color: var(--g-bg);
}

.title {
  color: var(--g-text-color-1);
}

.account-info {
  color: var(--g-text-color-2);
}
</style>

<script setup lang="ts">
import { reactive } from 'vue';
import InstrumentInput from '@/components/common/InstrumentInput.vue';
import TradeDirection from '@/components/NormalTradeView/TradeTabs/TradeDirection.vue';
import type { AccountInfo, InstrumentInfo } from '@/types';
import { TradeDirectionEnum } from '@/enum';

const { selectedAccount } = defineProps<{
  selectedAccount?: AccountInfo;
}>();

const selectedInstrument = defineModel<InstrumentInfo>('instrument');

const form = reactive({
  direction: TradeDirectionEnum.买入,
  price: 0,
  volume: 0,
});
</script>

<template>
  <div class="trading-panel" h-full flex flex-col>
    <!-- 买卖方向 -->
    <div class="direction-section" p-16>
      <TradeDirection v-model="form.direction" />
    </div>
    <!-- 合约选择 -->
    <div class="form-item" mb-16>
      <label class="label">代码</label>
      <InstrumentInput
        v-model="selectedInstrument"
        :asset-type="selectedAccount?.assetType"
        placeholder="请输入合约代码或名称"
      />
    </div>
  </div>
</template>

<style scoped></style>

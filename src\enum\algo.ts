import { enumToArray } from '../script';

/**
 * 算法执行时间
 */
export enum AlgorithmExecuteTimeEnum {
  立即执行 = 1,
  指定时间 = 2,
  当日有效 = 3,
}

/**
 * 算法执行时间
 */
export const AlgorithmExecuteTimes = enumToArray(AlgorithmExecuteTimeEnum);

/**
 * 到期未成处理
 */
export enum ExpirationUnfinishedTreatmentEnum {
  自动撤销 = 1,
  转为限价 = 2,
}

/**
 * 到期未成处理
 */
export const ExpirationUnfinishedTreatments = enumToArray(ExpirationUnfinishedTreatmentEnum);

/**
 * 交易风格
 */
export enum TradeStyleEnum {
  激进型 = 1,
  稳健型 = 2,
  保守型 = 3,
}

/**
 * 交易风格
 */
export const TradeStyles = enumToArray(TradeStyleEnum);

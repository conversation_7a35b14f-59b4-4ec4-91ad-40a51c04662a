<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onMounted, shallowRef, useTemplateRef, watch } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition } from '@/types';
import type { HistoryQueryOptions, LegacyFundInfo, OrderInfo } from '../../../../../xtrade-sdk';
import { IdentityType, Repos } from '../../../../../xtrade-sdk/dist';
import {
  userNameCol,
  instrumentCol,
  instrumentNameCol,
  tradingDayCol,
  orderStatusCol,
  directionCol,
  businessFlagCol,
  volumeOriginalCol,
  orderPriceCol,
  tradedVolumeCol,
  tradedPriceCol,
  createTimeCol,
  orderTimeCol,
  tradeTimeCol,
  exchangeOrderIdCol,
  frozenMarginCol,
  fundNameCol,
  assetTypeCol,
  foreignCol,
  forceCloseCol,
  remarkCol,
  accountNameCol,
} from './shared/columnDefinitions';
import DateRangePicker from './shared/DateRangePicker.vue';
import { getDefaultDateRange } from '@/script';

const recordsRepo = new Repos.RecordsRepo();

// 定义组件属性
const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : LegacyFundInfo;
}>();

// 基础列定义
const baseColumns = [
  userNameCol,
  instrumentCol,
  instrumentNameCol,
  tradingDayCol,
  orderStatusCol,
  directionCol,
  businessFlagCol,
  volumeOriginalCol,
  orderPriceCol,
  tradedVolumeCol,
  tradedPriceCol,
  createTimeCol,
  orderTimeCol,
  tradeTimeCol,
  exchangeOrderIdCol,
  frozenMarginCol,
  fundNameCol,
  assetTypeCol,
  foreignCol,
  forceCloseCol,
  remarkCol,
] as ColumnDefinition<OrderInfo>;

// 根据类型动态生成列
const columns = computed(() => {
  const cols = [...baseColumns];
  if (type === 'product') {
    cols.unshift(accountNameCol as any);
  }
  return cols;
});

// 历史订单数据
const historyOrders = shallowRef<OrderInfo[]>([]);
const tableRef = useTemplateRef('tableRef');

// 日期范围
const dateRange = shallowRef<[string, string]>(getDefaultDateRange());

// 获取历史订单数据
const fetchHistoryOrders = async () => {
  if (!activeItem) return;

  // 构建查询参数
  const options = {
    pageNo: 1,
    pageSize: 100000,
  } as HistoryQueryOptions;

  // 根据类型设置查询参数
  if (type === 'account') {
    options.account_id = activeItem.id;
  } else {
    options.fund_id = activeItem.id;
  }

  options.begin_day = dateRange.value[0];
  options.end_day = dateRange.value[1];

  // 调用SDK接口
  const identityType = type === 'account' ? IdentityType.Account.value : IdentityType.Fund.value;
  const response = await recordsRepo.QueryHistoryOrders(identityType, options);

  if (response && response.data) {
    historyOrders.value = response.data.list;
  } else {
    historyOrders.value = [];
  }
};

const remoteMethod = async (pageNo: number) => {
  if (!activeItem) return;

  // 构建查询参数
  const options = {
    pageNo,
    pageSize: 30,
  } as HistoryQueryOptions;

  // 根据类型设置查询参数
  if (type === 'account') {
    options.account_id = activeItem.id;
  } else {
    options.fund_id = activeItem.id;
  }

  options.begin_day = dateRange.value[0];
  options.end_day = dateRange.value[1];

  // 调用SDK接口
  const identityType = type === 'account' ? IdentityType.Account.value : IdentityType.Fund.value;
  const response = await recordsRepo.QueryHistoryOrders(identityType, options);

  if (response && response.data) {
    historyOrders.value = response.data.list;
  } else {
    historyOrders.value = [];
  }
};

// 导出数据
const exportData = () => {
  // 实现导出功能
  console.log('导出历史订单数据');
};

onMounted(() => {
  if (activeItem) {
    // fetchHistoryOrders();
  }
});

// 监听activeItem变化
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      // fetchHistoryOrders();
    }
  },
  { deep: true },
);

// 监听日期范围变化
watch(dateRange, (newValue, oldValue) => {
  if (newValue[0] !== oldValue[0] || newValue[1] !== oldValue[1]) {
    // fetchHistoryOrders();
  }
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'orderTime', order: TableV2SortOrder.DESC }"
    :columns
    :data="historyOrders"
    fixed
    :remote-method="remoteMethod"
  >
    <template #left>
      <DateRangePicker v-model="dateRange" />
    </template>
    <template #actions>
      <div class="actions" flex aic>
        <el-button @click="fetchHistoryOrders" size="small" color="var(--g-primary)">
          查询
        </el-button>
        <el-button @click="exportData" size="small">导出</el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>

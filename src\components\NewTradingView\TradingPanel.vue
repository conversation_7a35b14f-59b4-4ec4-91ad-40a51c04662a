<script setup lang="ts">
import { defineAsyncComponent } from 'vue';
import type { AccountInfo, InstrumentInfo } from '@/types';
const NormalPanel = defineAsyncComponent(() => import('./NormalPanel.vue'));
const AlgorithmPanel = defineAsyncComponent(() => import('./AlgorithmPanel.vue'));
const BasketPanel = defineAsyncComponent(() => import('./BasketPanel.vue'));

const { selectedAccount } = defineProps<{
  selectedAccount?: AccountInfo;
}>();

// 交易类型
const tradeType = defineModel<'normal' | 'algo' | 'basket'>('tradeType');

// 选中合约
const instrument = defineModel<InstrumentInfo>('instrument');

// 交易类型选项
const tradeTypeOptions = [
  { label: '普通交易', value: 'normal' },
  { label: '算法交易', value: 'algo' },
  { label: '篮子交易', value: 'basket' },
];
</script>
+

<template>
  <div h-full flex="~ col">
    <!-- 交易类型选择 -->
    <div class="trade-type-section" p-16>
      <el-radio-group v-model="tradeType">
        <el-radio-button
          v-for="option in tradeTypeOptions"
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </el-radio-button>
      </el-radio-group>
    </div>

    <NormalPanel
      v-if="tradeType === 'normal'"
      v-model:instrument="instrument"
      :selected-account="selectedAccount"
    />
    <AlgorithmPanel
      v-else-if="tradeType === 'algo'"
      v-model:instrument="instrument"
      :selected-account="selectedAccount"
    />
    <BasketPanel
      v-else-if="tradeType === 'basket'"
      v-model:instrument="instrument"
      :selected-account="selectedAccount"
    />
  </div>
</template>

<style scoped></style>

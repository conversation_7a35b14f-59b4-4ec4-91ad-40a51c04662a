<script setup lang="ts">
import { ref, provide } from 'vue';
import AccountList from '@/components/common/ComponentTabs/AccountList.vue';
import TradingPanel from '@/components/NewTradingView/TradingPanel.vue';
import MarketDepth from '@/components/NewTradingView/MarketDepth.vue';
import AccountDetails from '@/components/NewTradingView/AccountDetails.vue';
import type { AccountInfo, InstrumentInfo } from '@/types';
import { INSTRUMENT_SELECT_KEY } from '@/keys';

// 选中合约，依赖注入到InstrumentInput组件中
const instrumentSelect = ref<string>('');
provide(INSTRUMENT_SELECT_KEY, instrumentSelect);

// 当前选中的账号
const selectedAccount = ref<AccountInfo>();

// 当前选中的合约
const selectedInstrument = ref<InstrumentInfo>();

// 交易类型：普通交易、算法交易、篮子交易
const tradeType = ref<'normal' | 'algo' | 'basket'>('normal');

// 处理账号选择
const handleAccountSelect = (account: AccountInfo) => {
  selectedAccount.value = account;
};
</script>

<template>
  <div class="new-trading-view" flex="~ col" h-full>
    <!-- 上半部分：账号列表 -->
    <div class="trader-overview-section" h-300>
      <AccountList
        trade
        @row-click="handleAccountSelect"
        @account-ids-change="$emit('account-ids-change', $event)"
      />
    </div>

    <!-- 下半部分：三个面板 -->
    <div class="trading-panels" bg="[--g-block-bg-2]" flex flex-1 min-h-1>
      <!-- 左侧：下单界面 -->
      <div class="trading-panel-section" w-400>
        <TradingPanel
          :selected-account="selectedAccount"
          v-model:instrument="selectedInstrument"
          v-model:tradeType="tradeType"
        />
      </div>

      <!-- 中间：盘口 -->
      <div class="market-depth-section" w-300>
        <MarketDepth :selected-instrument="selectedInstrument" />
      </div>

      <!-- 右侧：账号详情 -->
      <div class="account-details-section" flex-1 min-w-1>
        <AccountDetails :selected-account="selectedAccount" :trade-type="tradeType" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.new-trading-view {
  background-color: var(--g-bg);
}
</style>
